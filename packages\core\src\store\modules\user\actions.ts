import * as api from './api'
import { useEnvConfig, useApi, useWindow } from 'hook'
import { inApp, getUserInfo, logEventStatistics, AliLog, getSystem } from '@via/mylink-sdk'
import { useStorage, useLang, useRouter } from 'hook'
import { userType } from './type'
import * as  Sentry  from '@sentry/vue'
const { customLoad, remove } = useStorage()
const { router } = useRouter()
const envConfig = useEnvConfig()
const { setJwt } = useApi()

// 判断大陆或内地
const getRegionalApkStatus = () => {
  return new Promise((resolve, reject) => {
    if (inApp.value) {
      try {
        const OS = getSystem()
        window.getApkStatus = (info) => {
          resolve(info)
        }
        const params = JSON.stringify({
          callbackName: 'getApkStatus'
        })
        if (OS === 'android') {
          window.HkAndroid?.getRegionalApkStatus(params)
        } else if (OS === 'ios') {
          window.webkit?.messageHandlers?.getRegionalApkStatus.postMessage(params)
        } else {
          console.log('getSystem()既非android,也非ios,请检查navigator.userAgent是否符合getSystem代码逻辑')
          resolve(0)
        }
      } catch (error) {
        console.log('getRegionalApkStatus错误：', error)
        resolve(0)
      }
    } else {
      // resolve => 1-大陆 0-香港
      resolve(0)
    }
  })
}

async function login(this: userType, toLog:boolean = true) {
    remove('login-jwt')//删掉之前登录过的jwt
    let token = envConfig.RUN_ENV == 'develop' ? envConfig.DEFAULT_TOKEN : ''
    this.isHK = await getRegionalApkStatus() as number
    if(envConfig.RUN_ENV != 'develop'){
      if (inApp.value) {
        const userInfo = await getUserInfo(toLog)
        console.log(userInfo);
        if (userInfo && userInfo.authorization) {
          token = userInfo.authorization
          this.headLogo = userInfo.user.headLogo || this.headLogo
          this.frameUrl = userInfo.user.headLogoFrameUrl || ''
          this.phone = userInfo.user.phone || ''
          Sentry.setTag('phone',this.phone)
          this.name = userInfo.user.nickname || ''
          AliLog.setPhone(this.phone)
        }else{
          return
        }
      }else{
        router.replace({
          path:'/share',
            query: {
                "hideNavigationBar": 'true'
            }
        })
      }
    }else{
      this.inLogin = true
      setJwt('eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyODM2NTU0ODc3Njc4Mzg5NSIsImV4cCI6MTc4MDgxNzc2MSwiaWF0IjoxNzM3NjE3NzYxfQ.L-auR_C_k0-M3MtJhQLFJMCOY9jzszJkEa5jZiWOL7lOnC8_yyrjfDLQJfag4qz8iUT3WTPnI9_ltxPX4_IkKw')
      setJwt('eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyODUwNDQ3NjUwNjg0OTI5MCIsImV4cCI6MTc4Mjk3NTg0MSwiaWF0IjoxNzM5Nzc1ODQxfQ.6nYRxDqcM7op1NqOS_q0QA2MWHPnSEpgbrag-nCd30cEzZ9UQoDyiDAhm6CfoL8pj-To6en3NFpikya0uL7XIw')
      setJwt('eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyODM2NTczNDI4MjQ2MTIwOSIsImV4cCI6MTc4Mjk3NjkwNywiaWF0IjoxNzM5Nzc2OTA3fQ.FAcauPnLD1PHcGHD8ziFnfP6RK1M_Z5m_Y3z-GQBBU6jwnHdNit0VUrUPx3ubwDm4PnvsByvPCk3f85SOA-Sgw')
      setJwt('eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyODM4NjcwNzY4MTUwOTM3OSIsImV4cCI6MTc4MzIxNjI3NCwiaWF0IjoxNzQwMDE2Mjc0fQ._NLcLN3sLPE1xrVfZDLnNksinVlH1f4JlPJdonzMRroJQS9EZQjByCEB7XcO42fY0eXVaCrQqpAcDSLnEb9B1w')
      setJwt('eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyODUwMDM5NzM5NTIxNDM0MSIsImV4cCI6MTc4MzIxOTE4MCwiaWF0IjoxNzQwMDE5MTgwfQ.6rToTcJaCh6sIkdphMZwxH7BcH7l8RYv7nJWYWXzVco_m4tFL9JljEQsvlALbtqqyDMANRNq9QWNDo4dji6dJA')
      // setJwt('eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyODM4NjcwNzY4MTUwOTM3OSIsImV4cCI6MTc4MzIyMDY4MCwiaWF0IjoxNzQwMDIwNjgwfQ.nSNuMgg-GVYe6zCDt_iSVz5BnXmvcTxdJAbWTOQDm-jwSzCz09R1vFJTqH0Fr23D7Vf4y6Ar-528B7bFhDXa6g')
      setJwt('eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyODM4NjcwNzY4MTUwOTM3OSIsImV4cCI6MTc4MzIyMDY4MCwiaWF0IjoxNzQwMDIwNjgwfQ.nSNuMgg-GVYe6zCDt_iSVz5BnXmvcTxdJAbWTOQDm-jwSzCz09R1vFJTqH0Fr23D7Vf4y6Ar-528B7bFhDXa6g')
      setJwt('eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyODQ0NjQyMDk5NzcwMTY0OSIsImV4cCI6MTc4NjMzMzgzOSwiaWF0IjoxNzQzMTMzODM5fQ.zbNg3iyBff7BAG5RXZEDuAuu60-Ef6kGUUWfmO5VKb0fzaXiqH0Op35XhoNBOJhKI9BMx_81Vjv6Dc4U9N3E5Q')
      setJwt('eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyODU2NDA4NTg3MTQxMTI0MyIsImV4cCI6MTc4ODE0MjUwMywiaWF0IjoxNzQ0OTQyNTAzfQ.9xi3inWQc8_K1PaTMfosUmPbEqqut11oXcV7JCdpUt70EChJDaxgHMBkTH6UTzEkl1Nin5hwVyxjcsKI4vb90w')
      setJwt('eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyODU3NTM1NzM3NTU0NTM4NSIsImV4cCI6MTc4OTA5OTE1NywiaWF0IjoxNzQ1ODk5MTU3fQ.ihYFky4Fae0FNlO0IJyUxMClJDbUrL9MRJJkgrIuCq1s9adOKoG_QVz7aWcDPxgOQ81pFsC_gpH1N0b1xgbA6Q')
      // return
    }
    return await api
      .login(token,this.isHK)
      .then((res) => {
        setJwt(res.token)
        this.inLogin = true
      })
      .catch((err) => {
        if(envConfig.RUN_ENV != 'develop'){
          router.replace({
            path:'/share',
              query: {
                  "hideNavigationBar": 'true'
              }
          })
        }
      })
}


function changeVip(this: userType, boo: boolean) {
  this.isVip = boo
}

function changeSlash(this: userType, boo: boolean) {
  this.isSlash = boo
}

function getToken() {
  let token = customLoad('via:system:login-jwt')
  return token.v
}

function changeUsingCard(this: userType, boo: boolean){
  this.usingCard = boo
}

// 设置每日活动提醒标志
async function updateNotifiedActivity(){
  return await api.updateNotifiedActivity()
    .then((res) => {
      logEventStatistics('garden_activity_icon_click')
      return res
    })
    .catch((err) => {
      console.log(err)
    })
}

// 设置首次登陆提醒标志
async function updateFirstLogin(){
  return await api.updateFirstLogin()
    .then((res) => {
      return res
    })
    .catch((err) => {
      console.log(err)
    })
}

// 设置二期活动首次登陆提醒标志
async function updateFirstLogin1(){
  return await api.updateFirstLogin1()
    .then((res) => {
      return res
    })
    .catch((err) => {
      console.log(err)
    })
}

// 设置三期活动首次登陆提醒标志
async function updateFirstLogin2(){
  return await api.updateFirstLogin2()
    .then((res) => {
      return res
    })
    .catch((err) => {
      console.log(err)
    })
}

async function updateFirstLogin3(){
  return await api.updateFirstLogin3()
    .then((res) => {
      return res
    })
    .catch((err) => {
      console.log(err)
    })
}

async function updateFirstLogin4(){
  return await api.updateFirstLogin4()
    .then((res) => {
      return res
    })
    .catch((err) => {
      console.log(err)
    })
}



async function guildCom(this: userType) {
  return await api.guildCom()
    .then((res) => {
      this.isGuild = true
      // 新增使用我的花園人數
      logEventStatistics('Mygarden_new_user')
      return res
    })
    .catch((err) => {
      console.log(err)
    })
}

// 完成新手引导
function changeGuild(this: userType, boo: boolean){
  this.isGuild = boo
}

// 设置背包提醒标志
async function updatePackageFlag(){
  return await api.updatePackageFlag()
  .then((res) => {
    return res
  })
  .catch((err) => {
    console.log(err)
  })
}

// 设置背包提醒标志(功能卡)
async function updatePackageFuncFlag(){
  return await api.updatePackageFuncFlag()
  .then((res) => {
    return res
  })
  .catch((err) => {
    console.log(err)
  })
}

// 设置背包提醒标志(vip功能卡)
async function updatePackageFuncFlag1(){
  return await api.updatePackageFuncFlag1()
  .then((res) => {
    return res
  })
  .catch((err) => {
    console.log(err)
  })
}

async function setUpData(this: userType,manage_key,manage_value) {
  if (!this.inLogin) {
    return
  }
  return api.update(manage_key,manage_value)
}

async function wartering() {
  return await api.watering()
}


export default {
    login,
    changeVip,
    getToken,
    changeGuild,
    guildCom,
    updateNotifiedActivity,
    updateFirstLogin,
    updateFirstLogin1,
    updateFirstLogin2,
    updateFirstLogin3,
    updateFirstLogin4,
    changeUsingCard,
    updatePackageFlag,
    updatePackageFuncFlag,
    updatePackageFuncFlag1,
    changeSlash,
    setUpData,
    wartering
}
