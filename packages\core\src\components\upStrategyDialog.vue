<template>
    <div class="upStrategyDialog">
        <div class="title">
            {{ props.title }}
        </div>
        <p v-html=props.detail></p>
        <div v-if="type === 'recharge'" class="button recharge" @click="recharge">{{ state.ai.去充值 }}</div>
        <div v-else class="button" @click="eventBus.emit('closeUpStrategyDialog')">{{ state.bearTalk.知道了 }}</div>
    </div>
</template>

<script setup lang='ts'>
import { useLang, useEventBus,useEnvConfig } from 'hook'
const { state } = useLang()
const env = useEnvConfig()
const eventBus = useEventBus()
const props = defineProps({
    title:{
        type:String,
        require:true
    },
    detail:{
        type:String,
        require:true
    },
    type:{
        type:String,
        require:true
    }
})

const recharge = ()=>{
    eventBus.emit('closeUpStrategyDialog')
    if (env.RUN_ENV == 'develop' || env.RUN_ENV == 'uat') {
        location.href = 'http://*************/activity/2daigc/#/photo/profile?forceHideNavigationBar=true&lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&isRecharge=1'
    }else{
        location.href = 'https://mylink.komect.com/activity/2daigc/#/photo/profile?forceHideNavigationBar=true&lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&isRecharge=1'
    }
}
</script>

<style lang='less' scoped>
.upStrategyDialog {
    padding: 40px;
    font-family: PingFang SC, PingFang SC;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
        font-weight: 600;
        font-size: 32px;
        color: #4E5B7E;
        line-height: 40px;
        text-align: center;
        margin-bottom: 32px;
    }

    p {
        font-weight: 400;
        font-size: 28px;
        color: #4E5B7E;
        line-height: 40px;
        text-align: left;
        margin-bottom: 50px;
    }

    .button {
        width: 568px;
        height: 84px;
        background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
        box-shadow: 0px 8px 0px 2px #FCAF28, inset 0px 4px 0px 2px #FFF2B2;
        border-radius: 48px 12px 48px 12px;
        line-height: 84px;
        font-weight: bold;
        font-size: 36px;
        color: #FFFFFF;
        text-align: center;
    }
    .recharge{
        background: linear-gradient( 180deg, #9F78F5 0%, #7D55F5 100%);
        box-shadow: 0px 8px 0px 2px #6338DF, inset 0px 4px 0px 2px #BAB2FF;
    }
}
</style>
