<template>
  <div class="box">
        <p class="tip" v-if="awordArr[index].code != 'energy10'">{{state.联动奖赏.後付限定}}</p>
        <p class="tip" v-else>{{state.联动奖赏.本月后付用户赠礼}}</p>
        <p v-if="awordArr.length > 0" class="tip1">{{ "(" + (index+1) + "/" + awordArr.length + ")"}}</p>
        <img class="hhaa ll" @click.stop="iknew" v-if="index > 0" :src="$imgs['j1.png']" alt="">
        <img class="hhaa rr" @click.stop="iknow" v-if="index < awordArr.length-1" :src="$imgs['j1.png']" alt="">
        
        <div class="cardContainer">
            <div class="cardItem">
                <p class="timeOut">{{dayjs(awordArr[index].destory_at).format('YYYY.MM.DD')}}{{state.home.过期}}</p>
                <img class="card" :src="$imgs[`${awordArr[index].code == 'energy10' ? '10gCard' :'awordbg'}.png`]" alt="">
                <img class="typeImg" :src="$imgs[`${awordArr[index].code == 'x2wewalk' ? 'Double_step' : 
                                                    awordArr[index].code == 'x2energy' ? 'reduction_card' :
                                                    awordArr[index].code == 'energy20' ? '碳值20' :
                                                    awordArr[index].code == 'energy30' ? '碳值30' : 
                                                    awordArr[index].code == 'energy10' ? '' : ''
                                                    }.png`]" alt="">
                <p class="cardname">{{
                    awordArr[index].code == 'x2wewalk' ? state.home.步数单日双倍卡 : 
                    awordArr[index].code == 'x2energy' ? state.home.减碳值单日双倍卡    :
                    awordArr[index].code == 'energy20' ? state.联动奖赏.碳值卡20g :
                    awordArr[index].code == 'energy30' ? state.联动奖赏.碳值卡30g : 
                    awordArr[index].code == 'energy10' ? state.联动奖赏.碳值卡10g : ''
                }}</p>
            </div>
        </div>
        <div class="knowbox">
            <div @click.stop="toPackage" class="know">
                {{state.联动奖赏.开心收下}}
            </div>
        </div>
        
        <p class="fanhui">{{state.dialog.点击屏幕返回}}</p>
  </div>
</template>

<script setup lang='ts'>
import { useLang, useDialog, useDayjs,useEventBus } from 'hook';
import { logEventStatistics } from '@via/mylink-sdk'
import { inject, ref,onBeforeMount } from 'vue'
import { useUserStore } from '@/store';
const { state } = useLang()
const { dayjs } = useDayjs()
let eventBus = useEventBus()
const props = defineProps({
  awordArr: {
      type: Array<any>,
      required: true
  }
})

let index = ref(0)
const uuid = inject('uuid')

const toPackage = () => {
    logEventStatistics('garden_blindbox_backpack_click')
    useDialog().getInstance(uuid)?.emit('toPackage')
}

const iknow = () => {
    if(index.value == props.awordArr.length-1){
    }else{
        index.value ++
    }
}

onBeforeMount(() => {
    const mask = document.querySelector('.click-mask')
    mask?.addEventListener('click',getAward,false)
})

const getAward = () => {
    if(useUserStore().isHK == 0){
        eventBus.emit('getPreAward')
    }
    const mask = document.querySelector('.click-mask')
    mask?.removeEventListener('click',getAward,false)
    return
}

const iknew = () => {
    if(index.value == 0){
    }else{
        index.value --
    }
}

</script>

<style lang='less' scoped>
.box{
    position: relative;
    display: flex;
    align-items: center;
    flex-direction: column;
    .hhaa{
        width: 38px;
        height: 61px;
        position: absolute;
        top: 344px;
        z-index: 2;
        &.ll{
            left: 100px;
        }
        &.rr{
            right: 100px;
            transform: rotate(180deg);
        }
        &.ll1{
            left: 100px;
            transform: rotate(180deg);
        }
        &.rr1{
            right: 100px;
        }
    }
    .tip{
        white-space: pre-line;
        text-align: center;
        font-size: 48px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        margin-bottom: 50px;
    }
    .tip1{
        margin-bottom: 30px;
        font-size: 28px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        line-height: 28px;
    }
    .cardContainer{
        display: flex;
        justify-content: center;
        width: 750px;
        .twoItem{
            width: 750px;
            display: flex;
            // justify-content: center;
            overflow: scroll;
            >.cardItem1{
                flex-shrink: 0;
            }
        }
        .cardItem{
            width: 334px;
            height: 488px;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            &.cardItem1{
                width: 314px;
                height: 458px;
                margin-left: 24px;
                margin-right: 12px;
                flex-shrink: 0;
                &:first-child{
                    margin-left: 40px;
                }
                .typeImg{
                    margin-top: 50px;
                }
            }
            .card{
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
            .typeImg{
                position: relative;
                height: 252px;
                margin-top: 100px;
            }
            .cardname{
                transform: translate(0, -30px);
                position: relative;
                font-size: 32px;
                font-family: PingFang TC-Semibold, PingFang TC;
                font-weight: 600;
                color: #4E5B7E;
                white-space: pre-line;
                text-align: center;
                display: flex;
                align-items: center;
                height: 155px;
                // background-color: pink;
            }
            .timeOut{
                z-index: 1;
                position: absolute;
                left: 50%;
                transform: translate(-50%, 0);
                font-size: 24px;
                font-family: PingFang SC-Semibold, PingFang SC;
                font-weight: 600;
                color: #FFFFFF;
                top: 10px;
                white-space: nowrap;
            }
        }
    }
    .bb{
        font-size: 28px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        padding-bottom: 34px;
        padding-top: 24px;
        text-align: center;
    }
    .knowbox{
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        margin-top: 98px;
        >.know{
            margin-bottom: 40px;
        }
        >.know:nth-child(2){
            background: linear-gradient(180deg, #56D3EB 0%, #34AADF 100%);
            box-shadow: 0px 8px 0px 2px rgba(56,120,185,1), inset 0px 4px 0px 2px rgba(140,239,247,1);
            margin-bottom: 0px;
        }
    }
    .know{
        min-width: 430px;
        height: 84px;
        background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
        box-shadow: 0px 8px 0px 2px rgba(252,175,40,1), inset 0px 4px 0px 2px rgba(255,242,178,1);
        border-radius: 48px 12px 48px 12px;
        font-size: 36px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        line-height: 32px;
        text-shadow: 0px 0px 12px #FBAC2E;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .fanhui{
        position: absolute;
        font-size: 28px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: -160px;
        white-space: nowrap;
    }
}
</style>