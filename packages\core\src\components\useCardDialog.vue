<template>
  <div class="box">
      <div v-if="type == 0 || type == 1" class="container container1" :class="style?'bg2':''">
          <img class="bg" :src="$imgs['card-long.png']" alt="" v-if="!style">
          <img :class="style?'chatu2':'chatu'" :src="$imgs[`${style? 'chatu2/':'chatu/'}${text.img}.png`]" alt="">
          <p class="big">{{
              type == 0 ? ( text.标题1 ) : 
              type == 1 ? ( isAgree ? state.home.使用成功 : text.标题2 ) : ''
          }}</p>
          <div :class="style?'small2':'small'">
              <p>{{!isAgree ? ( text.使用后1 ) : ( text.接下来1 )}}<br>
            <span>
                {{!isAgree ? ( text.使用后2 ) : ( text.接下来2 )}}
            </span>
            <span>{{!isAgree ? ( text.使用后3 ) : ( text.接下来3 )}}</span>
        </p>
            
          </div>
          <div :class="style?'btn2':'btn'" v-if="type == 0 || (type == 1 && isAgree)" @click="close">{{state.dialog.知道啦}}</div>
          <div :class="style?'btn2':'btn'" @click="agree(item)" v-if="(type == 1 && !isAgree)">{{state.home.确认使用}}</div>
      </div>
      <img class="xx" @click="emit('close')" :src="$imgs['xx.png']" alt="" v-if="!style">
  </div>
</template>

<script setup lang='ts'>
import { ref, inject, onBeforeMount } from 'vue';
import { useLoading, useLang, useDialog ,useToast, useEventBus } from 'hook';
import { usePropStore, useUserStore } from '@/store/index'
import { logEventStatistics } from '@via/mylink-sdk'
let isAgree = ref(false)
let loading = useLoading()
let { useCard } = usePropStore()
let { toast } = useToast()
let { state } = useLang()
let eventBus = useEventBus()

let text = ref({
    标题1: '',
    标题2: '',
    使用后1: '',
    使用后2: '',
    使用后3: '',
    接下来1: '',
    接下来2: '',
    接下来3: '',
    img: ''
})

let emit = defineEmits(['close', 'successUse'])
const props = defineProps({
    type: {
        type: Number,
        required: false
    },
    kind:{
        type: Number,
        required: false
    },
    item:{//type1才有
        type: Object,
        required: false
    },
    style:{ //样式改变
        type:Boolean,
        required:false,
        default:false
    }
})
const uuid = inject('uuid')

onBeforeMount(() => {
    switch(props.kind){
        case 0:
            text.value.标题1 = state.home.步数单日双倍卡
            text.value.标题2 = state.home.步数单日双倍卡
            text.value.使用后1 = state.home.使用后步数1
            text.value.使用后2 = state.home.使用后步数2
            text.value.使用后3 = state.home.使用后步数3
            text.value.接下来1 = state.home.接下来步数1
            text.value.接下来2 = state.home.接下来步数2
            text.value.接下来3 = state.home.接下来步数3
            text.value.img = 'walk_illustration'
            break
        case 1:
            text.value.标题1 = state.home.减碳值单日双倍卡
            text.value.标题2 = state.home.减碳值单日双倍卡
            text.value.使用后1 = state.home.使用后减碳值1
            text.value.使用后2 = state.home.使用后减碳值2
            text.value.使用后3 = state.home.使用后减碳值3
            text.value.接下来1 = state.home.接下来减碳值1
            text.value.接下来2 = state.home.接下来减碳值2
            text.value.接下来3 = state.home.接下来减碳值3
            text.value.img = 'reduction_Illustration'
            break
        case 2:
            text.value.标题1 = state.联动奖赏.碳值卡20g
            text.value.标题2 = state.联动奖赏.碳值卡20g
            text.value.使用后1 = state.联动奖赏.使用碳值卡后即时在201
            text.value.使用后2 = state.联动奖赏.使用碳值卡后即时在202
            text.value.使用后3 = state.联动奖赏.使用碳值卡后即时在203
            text.value.接下来1 = state.联动奖赏.使用碳值卡后即时在201
            text.value.接下来2 = state.联动奖赏.使用碳值卡后即时在202
            text.value.接下来3 = state.联动奖赏.使用碳值卡后即时在203
            text.value.img = '碳值20g'
            break
        case 3:
            text.value.标题1 = state.联动奖赏.碳值卡30g
            text.value.标题2 = state.联动奖赏.碳值卡30g
            text.value.使用后1 = state.联动奖赏.使用碳值卡后即时在301
            // text.value.使用后2 = state.联动奖赏.使用碳值卡后即时在302
            text.value.使用后3 = state.联动奖赏.使用碳值卡后即时在303
            text.value.接下来1 = state.联动奖赏.使用碳值卡后即时在301
            // text.value.接下来2 = state.联动奖赏.使用碳值卡后即时在302
            text.value.接下来3 = state.联动奖赏.使用碳值卡后即时在303
            text.value.img = '碳值30g'
            break
        case 4:
            text.value.标题1 = state.联动奖赏.碳值卡10g
            text.value.标题2 = state.联动奖赏.碳值卡10g
            text.value.使用后1 = state.联动奖赏.使用碳值卡后即时在101
            text.value.使用后3 = state.联动奖赏.使用碳值卡后即时在103
            text.value.接下来1 = state.联动奖赏.使用碳值卡后即时在101
            text.value.接下来3 = state.联动奖赏.使用碳值卡后即时在103
            text.value.img = '碳值10g'
            break
    }
})

const agree = async(item) => {
    if(useUserStore().usingCard && (item.code == 'x2wewalk' || item.code == 'x2energy')){
        toast(state.home.一张道具卡)
        emit('close')
        return
    }
    
    if(props.kind == 1){
        logEventStatistics('garden_double_crexp_use_click')
    }else if(props.kind == 0){
        logEventStatistics("garden_double_wewalk_crexp_use_click")
    }
    loading.loading('open')
    let res = await useCard(item.id)
    if(res.code){
        loading.loading('close')
        toast(res.msg)
        return
    }else{
        eventBus.emit('useCarding', res)
        isAgree.value = true
        useDialog().getInstance(uuid)?.emit('successUse')
        loading.loading('close')
    }
}

const close = () => {
    if(props.kind == 1){
        logEventStatistics('garden_double_crexp_ok_click')
    }else if(props.kind == 0){
        logEventStatistics("garden_double_wewalk_crexp_ok_click")
    }
    emit('close')
}

</script>

<style lang='less' scoped>
.box{
    position: relative;
    .xx{
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: -80px;
        width: 56px;
    }
    .container{
        position: relative;
        display: flex;
        align-items: center;
        // justify-content: center;
        flex-direction: column;
    }
    .bg2{
        width: 544px;
        height: 446px !important;
        background: #FFFFFF;
        border-radius: 24px 24px 24px 24px;
        opacity: 1;
    }
    .container1{
        width: 570px;
        height: 720px;
        display: flex;
        flex-direction: column;
        align-items: center;
        .bg{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        .chatu{
            width: 280px;
            height: 280px;
            position: relative;
            margin-top: 70px;
        }
        .chatu2{
            width: 120px;
            height: 120px;
            position: relative;
            margin-top: 48px;
        }
        .big{
            white-space: pre-line;
            position: relative;
            font-size: 32px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #4E5B7E;
            line-height: 45px;
            // margin-top: 26px;
            // margin-bottom: 10px;
            text-align: center;
        }
        .small2{
            flex: 1;
            // vertical-align: middle;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            font-size: 24px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #4E5B7E;
            width: 510px;
            text-align: center;
            margin: 16px 0 24px 0;
            >p>span{
                font-size: 18px;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                color: #196F20;
            }
        }
        .small{
            flex: 1;
            // vertical-align: middle;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            font-size: 25px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #4E5B7E;
            width: 510px;
            text-align: center;
            // >p>span{
            //     font-size: 18px;
            //     font-family: PingFang SC, PingFang SC;
            //     font-weight: 400;
            //     color: #196F20;
            // }
        }
        .btn2{
            margin-bottom: 54px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 220px;
            height: 56px;
            background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
            border-radius: 48px 48px 48px 48px;
            opacity: 1;
            font-size: 28px;
            font-family: PingFang SC, PingFang SC;
            font-weight: bold;
            color: #FFFFFF;
        }
        .btn{
            // position: absolute;
            // left: 50%;
            // transform: translate(-50%, 0);
            // bottom: 60px;
            margin-bottom: 60px;
            z-index: 1;
            width: 372px;
            height: 86px;
            background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
            box-shadow: inset 0px 4px 0px 2px rgba(255,242,178,1);
            border-radius: 48px 12px 48px 12px;
            font-size: 36px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #FFFFFF;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
</style>